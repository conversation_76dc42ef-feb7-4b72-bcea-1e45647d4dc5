import { GlobalNewsArticle } from './GlobalNewsService';
import { logger } from '../utils/logger';

export interface GeopoliticalEvent {
  type: 'conflict' | 'military_action' | 'energy_infrastructure' | 'economic_policy' | 'natural_disaster' | 'regulatory_change' | 'tech_breakthrough';
  region: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  keyEntities: string[];
  timeframe: 'immediate' | 'short_term' | 'medium_term' | 'long_term';
}

export interface SectorMapping {
  sector: string;
  impactType: 'direct' | 'indirect' | 'supply_chain' | 'sentiment' | 'regulatory';
  impactDirection: 'positive' | 'negative' | 'mixed';
  confidence: number;
  reasoning: string;
  affectedTickers: string[];
  causalChain: string[];
}

export interface GeopoliticalAnalysis {
  event: GeopoliticalEvent;
  sectorMappings: SectorMapping[];
  overallMarketImpact: number; // -100 to 100
  confidence: number;
  reasoning: string;
  tradingImplications: string[];
}

export class GeopoliticalAnalysisService {
  private static instance: GeopoliticalAnalysisService;

  public static getInstance(): GeopoliticalAnalysisService {
    if (!GeopoliticalAnalysisService.instance) {
      GeopoliticalAnalysisService.instance = new GeopoliticalAnalysisService();
    }
    return GeopoliticalAnalysisService.instance;
  }

  /**
   * Analyze if an article contains geopolitical events and map to market sectors
   */
  public analyzeGeopoliticalEvent(article: GlobalNewsArticle): GeopoliticalAnalysis | null {
    try {
      const event = this.classifyGeopoliticalEvent(article);
      if (!event) {
        return null;
      }

      const sectorMappings = this.mapEventToSectors(event, article);
      const overallMarketImpact = this.calculateOverallMarketImpact(sectorMappings);
      const confidence = this.calculateAnalysisConfidence(event, sectorMappings);
      const reasoning = this.generateAnalysisReasoning(event, sectorMappings);
      const tradingImplications = this.generateTradingImplications(event, sectorMappings);

      return {
        event,
        sectorMappings,
        overallMarketImpact,
        confidence,
        reasoning,
        tradingImplications
      };
    } catch (error) {
      logger.error('Error analyzing geopolitical event:', error);
      return null;
    }
  }

  /**
   * Classify the type of geopolitical event
   */
  private classifyGeopoliticalEvent(article: GlobalNewsArticle): GeopoliticalEvent | null {
    const content = `${article.title} ${article.content}`.toLowerCase();
    
    // Conflict patterns
    if (this.matchesPatterns(content, [
      'iran.*israel', 'israel.*iran', 'middle east.*conflict', 'gaza.*war', 'lebanon.*strike',
      'russia.*ukraine', 'ukraine.*russia', 'nato.*conflict', 'military.*strike',
      'missile.*attack', 'drone.*strike', 'air.*strike', 'bombing', 'invasion'
    ])) {
      return {
        type: 'conflict',
        region: this.extractRegion(content),
        severity: this.assessSeverity(content, 'conflict'),
        description: article.title,
        keyEntities: this.extractKeyEntities(content, 'conflict'),
        timeframe: 'immediate'
      };
    }

    // Energy infrastructure threats
    if (this.matchesPatterns(content, [
      'strait.*hormuz', 'oil.*supply', 'gas.*pipeline', 'energy.*infrastructure',
      'oil.*price', 'crude.*oil', 'natural.*gas', 'lng', 'opec', 'petroleum'
    ])) {
      return {
        type: 'energy_infrastructure',
        region: this.extractRegion(content),
        severity: this.assessSeverity(content, 'energy'),
        description: article.title,
        keyEntities: this.extractKeyEntities(content, 'energy'),
        timeframe: 'short_term'
      };
    }

    // Military actions
    if (this.matchesPatterns(content, [
      'military.*operation', 'defense.*spending', 'arms.*deal', 'weapon.*system',
      'fighter.*jet', 'missile.*defense', 'naval.*fleet', 'army.*deployment'
    ])) {
      return {
        type: 'military_action',
        region: this.extractRegion(content),
        severity: this.assessSeverity(content, 'military'),
        description: article.title,
        keyEntities: this.extractKeyEntities(content, 'military'),
        timeframe: 'medium_term'
      };
    }

    return null;
  }

  /**
   * Map geopolitical events to affected market sectors
   */
  private mapEventToSectors(event: GeopoliticalEvent, article: GlobalNewsArticle): SectorMapping[] {
    const mappings: SectorMapping[] = [];

    switch (event.type) {
      case 'conflict':
        mappings.push(...this.getConflictSectorMappings(event, article));
        break;
      case 'energy_infrastructure':
        mappings.push(...this.getEnergySectorMappings(event, article));
        break;
      case 'military_action':
        mappings.push(...this.getMilitarySectorMappings(event, article));
        break;
    }

    return mappings;
  }

  /**
   * Get sector mappings for conflict events
   */
  private getConflictSectorMappings(event: GeopoliticalEvent, article: GlobalNewsArticle): SectorMapping[] {
    return [
      {
        sector: 'Energy',
        impactType: 'indirect',
        impactDirection: 'positive',
        confidence: 75,
        reasoning: 'Geopolitical conflicts typically increase oil and gas prices due to supply concerns and regional instability, benefiting energy companies.',
        affectedTickers: ['XOM', 'CVX', 'COP', 'EOG', 'PXD', 'SLB'],
        causalChain: [
          'Geopolitical conflict creates regional instability',
          'Oil supply routes face potential disruption',
          'Market prices oil risk premium into crude prices',
          'Energy companies benefit from higher oil prices',
          'Stock prices of energy companies increase'
        ]
      },
      {
        sector: 'Defense',
        impactType: 'direct',
        impactDirection: 'positive',
        confidence: 80,
        reasoning: 'Military conflicts drive increased defense spending and demand for weapons systems, directly benefiting defense contractors.',
        affectedTickers: ['LMT', 'RTX', 'NOC', 'GD', 'BA', 'LHX'],
        causalChain: [
          'Military conflict escalates tensions',
          'Countries increase defense budgets',
          'Demand for weapons and defense systems rises',
          'Defense contractors receive more orders',
          'Defense company revenues and profits increase'
        ]
      },
      {
        sector: 'Safe Haven Assets',
        impactType: 'sentiment',
        impactDirection: 'positive',
        confidence: 70,
        reasoning: 'Geopolitical uncertainty drives investors toward safe-haven assets like gold and treasury bonds.',
        affectedTickers: ['GLD', 'TLT', 'SLV', 'GOLD', 'NEM', 'ABX'],
        causalChain: [
          'Geopolitical conflict increases market uncertainty',
          'Investors seek safe-haven assets',
          'Demand for gold and bonds increases',
          'Prices of safe-haven assets rise',
          'Related ETFs and mining companies benefit'
        ]
      }
    ];
  }

  /**
   * Get sector mappings for energy infrastructure events
   */
  private getEnergySectorMappings(event: GeopoliticalEvent, article: GlobalNewsArticle): SectorMapping[] {
    return [
      {
        sector: 'Oil & Gas',
        impactType: 'direct',
        impactDirection: 'positive',
        confidence: 85,
        reasoning: 'Energy infrastructure threats directly impact oil and gas supply, leading to higher prices and increased revenues for energy companies.',
        affectedTickers: ['XOM', 'CVX', 'COP', 'EOG', 'PXD', 'MPC', 'VLO', 'PSX'],
        causalChain: [
          'Energy infrastructure faces threats or disruption',
          'Oil and gas supply becomes constrained',
          'Energy prices increase due to supply concerns',
          'Energy companies benefit from higher commodity prices',
          'Stock valuations of energy companies rise'
        ]
      },
      {
        sector: 'Shipping & Logistics',
        impactType: 'supply_chain',
        impactDirection: 'mixed',
        confidence: 65,
        reasoning: 'Energy infrastructure disruptions affect shipping routes and logistics, creating both challenges and opportunities for shipping companies.',
        affectedTickers: ['FDX', 'UPS', 'STNG', 'TNK', 'NAT', 'EURN'],
        causalChain: [
          'Energy infrastructure disruption affects shipping routes',
          'Alternative routes become necessary',
          'Shipping costs and times increase',
          'Some shipping companies benefit from higher rates',
          'Others face operational challenges'
        ]
      }
    ];
  }

  /**
   * Get sector mappings for military action events
   */
  private getMilitarySectorMappings(event: GeopoliticalEvent, article: GlobalNewsArticle): SectorMapping[] {
    return [
      {
        sector: 'Aerospace & Defense',
        impactType: 'direct',
        impactDirection: 'positive',
        confidence: 90,
        reasoning: 'Military actions and defense spending directly benefit aerospace and defense companies through increased orders and contracts.',
        affectedTickers: ['LMT', 'RTX', 'NOC', 'GD', 'BA', 'LHX', 'HII', 'TXT'],
        causalChain: [
          'Military action demonstrates need for defense capabilities',
          'Government defense budgets increase',
          'Defense contracts and orders rise',
          'Defense companies see higher revenues',
          'Stock prices of defense companies appreciate'
        ]
      }
    ];
  }

  // Helper methods
  private matchesPatterns(content: string, patterns: string[]): boolean {
    return patterns.some(pattern => new RegExp(pattern, 'i').test(content));
  }

  private extractRegion(content: string): string {
    const regions = ['middle east', 'europe', 'asia', 'africa', 'americas'];
    for (const region of regions) {
      if (content.includes(region)) {
        return region;
      }
    }
    return 'global';
  }

  private assessSeverity(content: string, eventType: string): 'low' | 'medium' | 'high' | 'critical' {
    const highSeverityWords = ['war', 'invasion', 'bombing', 'strike', 'attack', 'crisis'];
    const mediumSeverityWords = ['tension', 'threat', 'concern', 'risk', 'dispute'];
    
    if (highSeverityWords.some(word => content.includes(word))) {
      return 'high';
    } else if (mediumSeverityWords.some(word => content.includes(word))) {
      return 'medium';
    }
    return 'low';
  }

  private extractKeyEntities(content: string, eventType: string): string[] {
    const entities: string[] = [];
    
    // Common geopolitical entities
    const geopoliticalEntities = ['iran', 'israel', 'russia', 'ukraine', 'china', 'usa', 'nato', 'opec'];
    geopoliticalEntities.forEach(entity => {
      if (content.includes(entity)) {
        entities.push(entity.toUpperCase());
      }
    });

    return entities;
  }

  private calculateOverallMarketImpact(mappings: SectorMapping[]): number {
    if (mappings.length === 0) return 0;
    
    const weightedImpacts = mappings.map(mapping => {
      const directionMultiplier = mapping.impactDirection === 'positive' ? 1 : 
                                 mapping.impactDirection === 'negative' ? -1 : 0;
      return (mapping.confidence / 100) * directionMultiplier * 50; // Scale to -50 to 50
    });

    return Math.round(weightedImpacts.reduce((sum, impact) => sum + impact, 0) / mappings.length);
  }

  private calculateAnalysisConfidence(event: GeopoliticalEvent, mappings: SectorMapping[]): number {
    if (mappings.length === 0) return 0;
    
    const avgMappingConfidence = mappings.reduce((sum, mapping) => sum + mapping.confidence, 0) / mappings.length;
    const severityBonus = event.severity === 'high' ? 10 : event.severity === 'medium' ? 5 : 0;
    
    return Math.min(100, Math.round(avgMappingConfidence + severityBonus));
  }

  private generateAnalysisReasoning(event: GeopoliticalEvent, mappings: SectorMapping[]): string {
    const sectorNames = mappings.map(m => m.sector).join(', ');
    return `Geopolitical ${event.type} event with ${event.severity} severity impacts multiple market sectors including ${sectorNames}. The event creates both direct and indirect market effects through supply chain disruptions, commodity price changes, and investor sentiment shifts.`;
  }

  private generateTradingImplications(event: GeopoliticalEvent, mappings: SectorMapping[]): string[] {
    const implications: string[] = [];
    
    mappings.forEach(mapping => {
      if (mapping.impactDirection === 'positive') {
        implications.push(`Consider long positions in ${mapping.sector} sector (${mapping.affectedTickers.slice(0, 3).join(', ')})`);
      } else if (mapping.impactDirection === 'negative') {
        implications.push(`Consider short positions or reduced exposure in ${mapping.sector} sector`);
      }
    });

    return implications;
  }
}
