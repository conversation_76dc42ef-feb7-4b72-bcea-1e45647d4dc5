import { GoogleGenerativeAI } from '@google/generative-ai';
import { GlobalNewsArticle } from './GlobalNewsService';
import { CompanyProfile } from './CompanyProfileService';
import { GeopoliticalAnalysisService } from './GeopoliticalAnalysisService';
import { logger } from '../utils/logger';

export interface MarketImpactAnalysis {
  impactScore: number; // 0-100
  impactDirection: 'positive' | 'negative' | 'neutral';
  timeframe: 'immediate' | 'short_term' | 'medium_term' | 'long_term';
  confidence: number; // 0-100
  affectedSectors: SectorImpact[];
  affectedCompanies: CompanyImpact[];
  reasoning: string;
  tradingOpportunities: TradingOpportunity[];
  riskFactors: string[];
  catalysts: string[];
}

export interface SectorImpact {
  sector: string;
  impactScore: number; // -100 to 100
  reasoning: string;
  confidence: number;
  timeframe: string;
}

export interface CompanyImpact {
  companyName: string;
  ticker?: string;
  impactScore: number; // -100 to 100
  reasoning: string;
  confidence: number;
  sector: string;
}

export interface TradingOpportunity {
  type: 'long' | 'short' | 'pairs_trade' | 'sector_rotation';
  tickers: string[];
  reasoning: string;
  expectedReturn: number;
  riskLevel: 'low' | 'medium' | 'high';
  timeframe: string;
  confidence: number;
}

export class NewsImpactAnalysisService {
  private genAI: GoogleGenerativeAI;
  private model: any;
  private geopoliticalAnalysisService: GeopoliticalAnalysisService;

  constructor(apiKey: string) {
    if (!apiKey) {
      throw new Error('Google AI API key is required');
    }
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemma-3-27b-it' });
    this.geopoliticalAnalysisService = GeopoliticalAnalysisService.getInstance();
  }

  /**
   * Analyze market impact of news article with enhanced validation
   */
  public async analyzeMarketImpact(
    article: GlobalNewsArticle,
    companyProfiles?: CompanyProfile[]
  ): Promise<MarketImpactAnalysis> {
    try {
      // First, check if this is a geopolitical event and enhance the analysis
      const geopoliticalAnalysis = this.geopoliticalAnalysisService.analyzeGeopoliticalEvent(article);

      if (geopoliticalAnalysis) {
        logger.info('🌍 Enhanced geopolitical analysis available', {
          eventType: geopoliticalAnalysis.event.type,
          sectorsIdentified: geopoliticalAnalysis.sectorMappings.length,
          overallImpact: geopoliticalAnalysis.overallMarketImpact
        });
      }

      const prompt = this.buildAnalysisPrompt(article, companyProfiles, geopoliticalAnalysis);
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Log the raw response from the AI for debugging
      console.log('=== RAW AI RESPONSE ===');
      console.log('Article:', article.title.substring(0, 100));
      console.log('Response:', text);
      console.log('=== END RAW RESPONSE ===');

      logger.info('Raw AI response received', {
        title: article.title.substring(0, 100),
        rawText: text.substring(0, 500) + (text.length > 500 ? '...' : '')
      });

      const analysis = this.parseAnalysisResponse(text);
      
      if (analysis.confidence > 0) {
        logger.info('Market impact analysis completed successfully', {
          title: article.title.substring(0, 100),
          impactScore: analysis.impactScore,
          affectedSectors: analysis.affectedSectors.length,
          opportunities: analysis.tradingOpportunities.length
        });
      } else {
        logger.warn('Market impact analysis returned fallback data', {
          title: article.title.substring(0, 100)
        });
      }

      return analysis;
    } catch (error) {
      logger.error('Error analyzing market impact:', error);
      return this.getFallbackAnalysis();
    }
  }

  /**
   * Build enhanced analysis prompt with business context validation
   */
  private buildAnalysisPrompt(article: GlobalNewsArticle, companyProfiles?: CompanyProfile[], geopoliticalAnalysis?: any): string {
    const companyContext = companyProfiles ? this.buildCompanyContext(companyProfiles) : '';
    const geopoliticalContext = geopoliticalAnalysis ? this.buildGeopoliticalContext(geopoliticalAnalysis) : '';

    return `
You are a professional financial analyst specializing in global market impact analysis with expertise in geopolitical events, macroeconomic trends, and sector-wide market dynamics. Your task is to analyze news articles for their potential impact on financial markets, focusing on both direct and indirect but legitimate market connections.

**NEWS ARTICLE:**
Title: ${article.title}
Content: ${article.content}
Source: ${article.source}
Published: ${article.publishedAt.toISOString()}
Category: ${article.category}

${companyContext}

${geopoliticalContext}

**GEOPOLITICAL & GLOBAL EVENT ANALYSIS FRAMEWORK:**
You excel at identifying how global events create market opportunities through sophisticated cause-and-effect analysis.

1. **Event Classification & Sector Mapping**
   - **Geopolitical Conflicts** (Iran-Israel, Russia-Ukraine, etc.) → Energy (oil/gas), Defense, Safe-haven assets, Regional banks
   - **Military Actions & Strikes** → Defense contractors, Energy infrastructure, Shipping/logistics, Aerospace
   - **Energy Infrastructure Threats** (Strait of Hormuz, pipelines) → Oil/gas companies, Refiners, Tanker companies, Alternative energy
   - **Economic Policy Changes** → Banking, Real estate, Consumer discretionary, Technology
   - **Natural Disasters** → Insurance, Construction, Utilities, Supply chain companies
   - **Regulatory Changes** → Affected industries, Compliance companies, Alternative solutions
   - **Technology Breakthroughs** → Tech companies, Disruptors, Traditional industries at risk

2. **Sophisticated Causal Chain Analysis**
   - **Primary Effects**: Direct impact on companies/sectors mentioned in news
   - **Secondary Effects**: Supply chain, commodity price, regulatory implications
   - **Tertiary Effects**: Market sentiment, risk-on/risk-off behavior, sector rotation
   - **Confidence Assessment**: Rate each causal link (40-100% for geopolitical events)

3. **Market Impact Mechanisms**
   - **Revenue Impact**: How does this affect company revenues (direct/indirect)?
   - **Cost Structure**: Supply chain disruptions, commodity price changes, regulatory costs
   - **Competitive Position**: Market share shifts, new opportunities, barriers to entry
   - **Investor Sentiment**: Risk perception changes, sector rotation, safe-haven flows
   - **Operational Impact**: Geographic exposure, supply chain dependencies, regulatory compliance

4. **Enhanced Sector Analysis**
   - Identify ALL sectors with legitimate exposure to the event
   - Consider indirect connections through supply chains and market dynamics
   - Include both positive and negative impacts across different sectors
   - Provide detailed multi-paragraph explanations for each sector connection

**RESPONSE FORMAT:**
Provide analysis in valid JSON with comprehensive geopolitical impact analysis:

\`\`\`json
{
  "impactScore": 0-100,
  "impactDirection": "positive|negative|neutral",
  "timeframe": "immediate|short_term|medium_term|long_term",
  "confidence": 0-100,
  "affectedSectors": [
    {
      "sector": "sector_name",
      "impactScore": -100 to 100,
      "reasoning": "detailed_multi_paragraph_causal_reasoning_explaining_how_global_event_affects_this_sector",
      "confidence": 40-100,
      "timeframe": "timeframe",
      "causalMechanism": "specific_mechanism_description",
      "impactType": "direct|indirect|supply_chain|sentiment|regulatory"
    }
  ],
  "affectedCompanies": [
    {
      "companyName": "company_name",
      "ticker": "TICKER",
      "impactScore": -100 to 100,
      "reasoning": "detailed_multi_paragraph_explanation_of_how_global_event_affects_this_company",
      "confidence": 40-100,
      "sector": "sector_name",
      "causalChain": {
        "newsToBusinessConfidence": 40-100,
        "businessToFinancialConfidence": 40-100,
        "financialToMarketConfidence": 40-100,
        "overallCausalStrength": 40-100
      },
      "businessImpactMechanism": "detailed_explanation_of_how_news_affects_business_operations",
      "geopoliticalExposure": "explanation_of_geographic_or_sector_exposure_to_the_event"
    }
  ],
  "reasoning": "comprehensive_analysis_explaining_the_global_event_and_its_market_implications",
  "tradingOpportunities": [
    {
      "type": "long|short|pairs_trade|sector_rotation",
      "tickers": ["TICKER1", "TICKER2"],
      "reasoning": "detailed_opportunity_explanation_with_geopolitical_context",
      "expectedReturn": "percentage",
      "riskLevel": "low|medium|high",
      "timeframe": "timeframe",
      "confidence": 40-100,
      "causalStrength": 40-100
    }
  ],
  "riskFactors": ["risk1", "risk2", "risk3"],
  "catalysts": ["catalyst1", "catalyst2", "catalyst3"],
  "geopoliticalContext": "explanation_of_the_broader_geopolitical_implications_and_market_dynamics"
}
\`\`\`

**GEOPOLITICAL ANALYSIS EXAMPLES:**
✅ CORRECT: Iran-Israel conflict → Energy sector (XOM, CVX, COP) due to oil supply concerns and regional instability
✅ CORRECT: Strait of Hormuz threats → Shipping companies (FDX, UPS) and tanker companies due to supply chain disruptions
✅ CORRECT: Military strikes → Defense contractors (LMT, RTX, NOC) due to increased defense spending expectations
✅ CORRECT: Energy infrastructure attacks → Alternative energy companies (TSLA, ENPH) due to energy security concerns

**ANALYSIS REQUIREMENTS:**
1. For geopolitical events, focus on sector-wide impacts rather than just direct company mentions
2. Consider indirect but real market connections (e.g., conflict → oil prices → energy companies)
3. Provide detailed multi-paragraph explanations for each sector and company connection
4. Use confidence scores of 40-100% for legitimate geopolitical impacts (lower threshold than direct business news)
5. Include comprehensive reasoning chains showing cause-and-effect relationships
6. Consider both positive and negative impacts across multiple sectors
7. Focus on material market impacts that professional traders would act upon

**CRITICAL SUCCESS FACTORS:**
- Identify ALL relevant sectors affected by the global event
- Provide sophisticated cause-and-effect reasoning chains
- Consider supply chain, commodity price, and sentiment impacts
- Include both immediate and longer-term market implications
- Map geopolitical events to affected market sectors systematically

Do not output any text before or after the JSON block.
`;
  }

  /**
   * Build company context for enhanced validation
   */
  private buildCompanyContext(companyProfiles: CompanyProfile[]): string {
    if (companyProfiles.length === 0) return '';

    const contextLines = companyProfiles.map(profile => {
      const revenueStreams = profile.revenueStreams
        .map(stream => `${stream.source} (${stream.percentage}%)`)
        .join(', ');

      return `${profile.ticker} (${profile.companyName}):
- Sector: ${profile.sector} | Industry: ${profile.industry}
- Business Model: ${profile.businessModel}
- Primary Revenue: ${revenueStreams || profile.primaryRevenue.join(', ')}
- Geographic Exposure: ${profile.geographicExposure.domestic}% domestic, ${profile.geographicExposure.international}% international
- Key Dependencies: Regulatory: ${profile.keyDependencies.regulatoryBodies.join(', ')}`;
    });

    return `
**COMPANY CONTEXT (Consider for sector-wide and geopolitical analysis):**
${contextLines.join('\n\n')}

**ANALYSIS GUIDANCE:** Consider how global events and sector-wide impacts affect these companies through direct operations, supply chains, commodity exposure, geographic presence, and market dynamics. Focus on legitimate market connections rather than just direct business model matches.`;
  }

  /**
   * Build geopolitical context for enhanced analysis
   */
  private buildGeopoliticalContext(geopoliticalAnalysis: any): string {
    if (!geopoliticalAnalysis) return '';

    const sectorMappings = geopoliticalAnalysis.sectorMappings.map((mapping: any) => {
      return `- **${mapping.sector}**: ${mapping.impactDirection} impact (${mapping.confidence}% confidence) - ${mapping.reasoning.substring(0, 200)}...
        Affected tickers: ${mapping.affectedTickers.join(', ')}`;
    }).join('\n');

    return `
**GEOPOLITICAL EVENT DETECTED:**
Event Type: ${geopoliticalAnalysis.event.type}
Severity: ${geopoliticalAnalysis.event.severity}
Region: ${geopoliticalAnalysis.event.region}
Description: ${geopoliticalAnalysis.event.description}

**PRE-ANALYZED SECTOR IMPACTS:**
${sectorMappings}

**ENHANCED ANALYSIS INSTRUCTION:** The above geopolitical analysis has identified specific sector impacts. Use this as a foundation and expand with additional sectors and companies that may be affected. Provide detailed reasoning for each connection.`;
  }

  /**
   * Parse AI analysis response
   */
  private parseAnalysisResponse(text: string): MarketImpactAnalysis {
    try {
      const jsonMatch = text.match(/```json([\s\S]*?)```/);
      if (jsonMatch && jsonMatch[1]) {
        const jsonString = jsonMatch[1].trim();
        const data = JSON.parse(jsonString);
        
        return {
          impactScore: data.impactScore || 0,
          impactDirection: data.impactDirection || 'neutral',
          timeframe: data.timeframe || 'short_term',
          confidence: data.confidence || 50,
          affectedSectors: data.affectedSectors || [],
          affectedCompanies: data.affectedCompanies || [],
          reasoning: data.reasoning || 'No analysis available',
          tradingOpportunities: data.tradingOpportunities || [],
          riskFactors: data.riskFactors || [],
          catalysts: data.catalysts || []
        };
      }

      // Fallback for cases where the AI doesn't use the markdown block
      const fallbackJsonMatch = text.match(/\{[\s\S]*\}/);
      if (fallbackJsonMatch) {
        logger.warn('Parsing fallback used; AI did not provide markdown JSON block.');
        const data = JSON.parse(fallbackJsonMatch[0]);
        return {
          impactScore: data.impactScore || 0,
          impactDirection: data.impactDirection || 'neutral',
          timeframe: data.timeframe || 'short_term',
          confidence: data.confidence || 50,
          affectedSectors: data.affectedSectors || [],
          affectedCompanies: data.affectedCompanies || [],
          reasoning: data.reasoning || 'No analysis available',
          tradingOpportunities: data.tradingOpportunities || [],
          riskFactors: data.riskFactors || [],
          catalysts: data.catalysts || []
        };
      }

      logger.error('Could not find or parse JSON in AI response', { responseText: text });
      return this.getFallbackAnalysis();
    } catch (error) {
      const e = error as Error;
      logger.error('Error parsing analysis response: ' + e.message, { 
        stack: e.stack,
        responseText: text 
      });
      return this.getFallbackAnalysis();
    }
  }

  /**
   * Fallback analysis for errors
   */
  private getFallbackAnalysis(): MarketImpactAnalysis {
    return {
      impactScore: 0,
      impactDirection: 'neutral',
      timeframe: 'short_term',
      confidence: 0,
      affectedSectors: [],
      affectedCompanies: [],
      reasoning: 'Analysis failed, unable to determine market impact',
      tradingOpportunities: [],
      riskFactors: ['Analysis uncertainty'],
      catalysts: []
    };
  }

  /**
   * Batch analyze multiple articles
   */
  public async batchAnalyzeArticles(
    articles: GlobalNewsArticle[],
    maxConcurrent: number = 5
  ): Promise<(GlobalNewsArticle & { analysis: MarketImpactAnalysis })[]> {
    const results: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[] = [];
    
    // Process articles in batches to avoid rate limits
    for (let i = 0; i < articles.length; i += maxConcurrent) {
      const batch = articles.slice(i, i + maxConcurrent);
      const batchPromises = batch.map(async (article) => {
        const analysis = await this.analyzeMarketImpact(article);
        return { ...article, analysis };
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Add delay between batches to respect rate limits
      if (i + maxConcurrent < articles.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  /**
   * Filter high-impact opportunities
   */
  public filterHighImpactOpportunities(
    analyzedArticles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[],
    minImpactScore: number = 60,
    minConfidence: number = 70
  ): TradingOpportunity[] {
    const opportunities: TradingOpportunity[] = [];

    analyzedArticles.forEach(article => {
      if (article.analysis.impactScore >= minImpactScore && 
          article.analysis.confidence >= minConfidence) {
        opportunities.push(...article.analysis.tradingOpportunities.filter(
          opp => opp.confidence >= minConfidence
        ));
      }
    });

    // Remove duplicates and sort by expected return
    const uniqueOpportunities = opportunities.filter((opp, index, self) => 
      index === self.findIndex(o => 
        o.type === opp.type && 
        JSON.stringify(o.tickers.sort()) === JSON.stringify(opp.tickers.sort())
      )
    );

    return uniqueOpportunities.sort((a, b) => b.expectedReturn - a.expectedReturn);
  }

  /**
   * Get sector impact summary
   */
  public getSectorImpactSummary(
    analyzedArticles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[]
  ): { [sector: string]: { totalImpact: number; articleCount: number; avgConfidence: number } } {
    const sectorSummary: { [sector: string]: { totalImpact: number; articleCount: number; avgConfidence: number } } = {};

    analyzedArticles.forEach(article => {
      article.analysis.affectedSectors.forEach(sectorImpact => {
        if (!sectorSummary[sectorImpact.sector]) {
          sectorSummary[sectorImpact.sector] = {
            totalImpact: 0,
            articleCount: 0,
            avgConfidence: 0
          };
        }

        sectorSummary[sectorImpact.sector].totalImpact += sectorImpact.impactScore;
        sectorSummary[sectorImpact.sector].articleCount += 1;
        sectorSummary[sectorImpact.sector].avgConfidence += sectorImpact.confidence;
      });
    });

    // Calculate averages
    Object.keys(sectorSummary).forEach(sector => {
      const summary = sectorSummary[sector];
      summary.avgConfidence = summary.avgConfidence / summary.articleCount;
    });

    return sectorSummary;
  }

  /**
   * Extract unique tickers from analysis
   */
  public extractTickers(
    analyzedArticles: (GlobalNewsArticle & { analysis: MarketImpactAnalysis })[]
  ): { ticker: string; impactScore: number; confidence: number; opportunities: number }[] {
    const tickerMap = new Map<string, { impactScore: number; confidence: number; opportunities: number; count: number }>();

    analyzedArticles.forEach(article => {
      // From affected companies
      article.analysis.affectedCompanies.forEach(company => {
        if (company.ticker) {
          const existing = tickerMap.get(company.ticker) || { impactScore: 0, confidence: 0, opportunities: 0, count: 0 };
          existing.impactScore += company.impactScore;
          existing.confidence += company.confidence;
          existing.count += 1;
          tickerMap.set(company.ticker, existing);
        }
      });

      // From trading opportunities
      article.analysis.tradingOpportunities.forEach(opportunity => {
        opportunity.tickers.forEach(ticker => {
          const existing = tickerMap.get(ticker) || { impactScore: 0, confidence: 0, opportunities: 0, count: 0 };
          existing.opportunities += 1;
          tickerMap.set(ticker, existing);
        });
      });
    });

    // Convert to array and calculate averages
    return Array.from(tickerMap.entries()).map(([ticker, data]) => ({
      ticker,
      impactScore: data.count > 0 ? data.impactScore / data.count : 0,
      confidence: data.count > 0 ? data.confidence / data.count : 0,
      opportunities: data.opportunities
    })).sort((a, b) => Math.abs(b.impactScore) - Math.abs(a.impactScore));
  }
}
