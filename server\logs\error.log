[2025-06-23 17:46:54.982] [ERROR] [General]: Port 3001 is already in use. | {"service":"trading-bot"}
[2025-06-23 17:46:56.215] [ERROR] [General]: Port 3001 is already in use. | {"service":"trading-bot"}
[2025-06-23 23:40:26.456] [ERROR] [General]: Port 3001 is already in use. | {"service":"trading-bot"}
[2025-06-23 23:42:10.224] [ERROR] [General]: Port 3001 is already in use. | {"service":"trading-bot"}
[2025-06-24 00:18:33.905] [ERROR] [General]: Error analyzing market impact: article.publishedAt.toISOString is not a function | {"service":"trading-bot","stack":"TypeError: article.publishedAt.toISOString is not a function\n    at NewsImpactAnalysisService.buildAnalysisPrompt (C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:69:34)\n    at NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:26:33)\n    at C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:295:45\n    at Array.map (<anonymous>)\n    at NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:294:41)\n    at C:\\work\\Trading Bot\\server\\dist\\routes\\discovery.js:103:58\n    at Layer.handle [as handle_request] (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)"}
[2025-06-24 00:21:43.680] [ERROR] [General]: Error analyzing market impact: article.publishedAt.toISOString is not a function | {"service":"trading-bot","stack":"TypeError: article.publishedAt.toISOString is not a function\n    at NewsImpactAnalysisService.buildAnalysisPrompt (C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:91:34)\n    at NewsImpactAnalysisService.analyzeMarketImpact (C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:26:33)\n    at C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:317:45\n    at Array.map (<anonymous>)\n    at NewsImpactAnalysisService.batchAnalyzeArticles (C:\\work\\Trading Bot\\server\\dist\\services\\NewsImpactAnalysisService.js:316:41)\n    at C:\\work\\Trading Bot\\server\\dist\\routes\\discovery.js:140:58\n    at Layer.handle [as handle_request] (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\work\\Trading Bot\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)"}
